# Clippy

Chatbot server

## Install

Créer un fichier `.env.local` et ajouter l'entrée `OPENAI_API_KEY` (cf. fichier .env)

Lancer la commande suivante :
```bash
phcstack clippy install
```

## Configuration de PyCharm Community Edition

- Installer un interpreteur local python :
```bash
apt install python3
```

Dans PyCharm, configurer un interpreteur python local :
- Aller dans `File > Settings > Python > Interpreter`
- Cliquer sur `Add Interpreter > Add local interpreter`
- Choisir l'interpréteur python3 installé précédemment
- Choisissez une 'Location' différente (ex: .host_venv)

## browse DBs

### MongoDB
http://localhost:8081 with user/pass : devteam/devteam   
Or connect to `mongodb://localhost:27017` with a mongo client (ex PHPStorm)

### ChromaDB
- browse the sqllite DB `var/data/chroma-db/chroma.sqlite3` (ex with PHPStorm) :
- get collections names from `collections.name` (ex: 'canned_responses_unembedded')
- open a shell in clippy-py container with `make exec`
- run ` HOME=/tmp chroma browse --host http://clippy-chroma:8000 <collection_name>`

Beware: pruning one of the data source (var/data/chroma-db/, var/data/chroma-db/, parlant-data/) without pruning all the others will lead to inconsistencies.