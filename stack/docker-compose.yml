# Supported by docker compose v2 but bugged in PHPStorm
# https://youtrack.jetbrains.com/issue/WI-73240/Interpreter-from-docker-compose-doesnt-work-if-docker-compose.yml-has-name-but-COMPOSEPROJECTNAME-isnt-set
# Add the environment variable in Interpreter settings: "COMPOSE_PROJECT_NAME=clippy"
name: 'clippy'

services:
    clippy-py:
        container_name: clippy-py
        restart: unless-stopped
        # Pour info, les images nommées " registry.gitlab.com/son-video/{project_name}/dev:{tag}" sont généralement construite à l'aide de la commande "registry" de phcstack : https://gitlab.com/son-video/payment-api/container_registry
        # Il peut y avoir d'autres images construites pour le projet, créés par le projet "docker" : https://gitlab.com/son-video/docker/container_registry
        image: registry.gitlab.com/son-video/clippy/dev:latest
        build:
            # Si le projet "docker" est installée dans un chemin relatif au projet en cours (../docker)
            # Vous "pouvez" build l'image directement via la commande `docker compose build {service_name}`
            # Cela peut échouer si l'image st multi-age, et, que l'image de base n'est pas présente sur votre machine, ou, dans le registre gitlab
            #
            # La commande `make registry` (ou `phcstack project_name registry`) va ignorer toutes ces informations
            # Et build l'image en fonction du nom utilisé dans la propriété "image"
            #
            # Exemple:
            # Pour une image avec un tag: registry.gitlab.com/son-video/my-project/dev:latest
            # La commande va build les 3 images suivantes (dans cette ordre):
            # - registry.gitlab.com/son-video/my-project/base:latest -> Image de base, jamais utilisée directement
            # - registry.gitlab.com/son-video/my-project/test:latest -> Image utilisée pour les tests, devrait etre renseigné dans les règles de la CI
            # - registry.gitlab.com/son-video/my-project/dev:latest -> Image utilisée pour le dev à partir de test
            #
            # Regex utilisée par docker stack : /registry.gitlab.com/(son-video/my-project/)(your_name)(:your_tag)/
            # Les images "base" and "test"sont toujours construites automatiquement et réutilisent le tag : (registry.gitlab.com/son-video/my-project/(base|test)(:your_tag)
            # Pour garder une cohérence avec tous nos projets, on essaie de garder "dev" dans ke nom et juste changer le tag de version si il y a besoin de tester une autre implémentation
            # Ce système ne fonctionne qu'avec les images du registre registry.gitlab.com
            #
            # Le projet "docker" fait ses propres builds selon ses propres règles, dans la CI, en fonction des règles de son propre gitlab.ci.yaml
            context: ../../docker/images/clippy
            dockerfile: Dockerfile.ci
            target: 'dev'
        volumes:
            - ../:/var/www/clippy
            - /etc/passwd:/etc/passwd:ro
            - /etc/group:/etc/group:ro
            - /etc/ssl/certs/ca-certificates.crt:/etc/ssl/certs/ca-certificates.crt:ro
        user: $USER_ID:$GROUP_ID
        working_dir: /var/www/clippy
        labels:
            - traefik.enable=true
            ## HTTPS
            - traefik.http.middlewares.clippy-https.redirectscheme.scheme=https
            - traefik.http.routers.clippy-https.entrypoints=ssl
            - traefik.http.routers.clippy-https.rule=Host(`clippy.dev`)
            - traefik.http.routers.clippy-https.tls=true
            ## HTTP
            - traefik.http.routers.clippy.entrypoints=web
            - traefik.http.routers.clippy.rule=Host(`clippy.dev`)
            ## Redirect HTTP -> HTTPS
            - traefik.http.routers.clippy.middlewares=clippy-https@docker
            ## PHCSTACK
            - phcstack.main=true
            - phcstack.registry.shared-with-marc=clippy
        networks:
            - default
            - docker-stack_webgateway
        command: watchmedo auto-restart --directory app --directory .venv --patterns='*.py;*.txt' --recursive --no-restart-on-command-exit uv run -- app/main.py
        depends_on:
          - clippy-mongo

    clippy-mongo:
        container_name: clippy-mongo
        image: mongo
        restart: unless-stopped
        ports:
            - 27017:27017
        user: $USER_ID:$GROUP_ID
        volumes:
          - ../var/data/mongo-db:/data/db
        networks:
            - default

    clippy-mongo-express:
        container_name: clippy-mongo-express
        image: mongo-express
        restart: unless-stopped
        ports:
            - 8081:8081
        environment:
            ME_CONFIG_MONGODB_URL: mongodb://clippy-mongo:27017/
            ME_CONFIG_BASICAUTH_ENABLED: true
            ME_CONFIG_BASICAUTH_USERNAME: devteam
            ME_CONFIG_BASICAUTH_PASSWORD: devteam
        networks:
            - default
        depends_on:
            - clippy-mongo

    clippy-chroma:
        container_name: clippy-chroma
        image: chromadb/chroma
        restart: unless-stopped
        #ports:
        #    - 8000:8000
        user: $USER_ID:$GROUP_ID
        volumes:
          - ../var/data/chroma-db:/data
        networks:
            - default

# use apps network
networks:
      default:
          name: clippy
      docker-stack_webgateway:
          external: true
