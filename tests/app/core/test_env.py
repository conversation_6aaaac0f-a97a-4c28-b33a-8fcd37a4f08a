import pytest
from unittest.mock import patch
from app.core.env import load_env


@pytest.mark.asyncio
async def test_load_env_dev():
    """Test load_env function with dev environment"""
    with patch('app.core.env.os.getenv') as mock_getenv, \
         patch('app.core.env.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = "dev"

        await load_env()

        # Verify correct .env files are loaded for dev
        assert mock_load_dotenv.call_count == 2
        mock_load_dotenv.assert_any_call(".env.local")
        mock_load_dotenv.assert_any_call(".env")


@pytest.mark.asyncio
async def test_load_env_test():
    """Test load_env function with test environment"""
    with patch('app.core.env.os.getenv') as mock_getenv, \
         patch('app.core.env.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = "test"

        await load_env()

        # Verify correct .env file is loaded for test
        mock_load_dotenv.assert_called_once_with(".env.test")


@pytest.mark.asyncio
async def test_load_env_production():
    """Test load_env function with production environment"""
    with patch('app.core.env.os.getenv') as mock_getenv, \
         patch('app.core.env.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = "prod"

        await load_env()

        # Verify default .env file is loaded for production
        mock_load_dotenv.assert_called_once_with(".env")


@pytest.mark.asyncio
async def test_load_env_default():
    """Test load_env function with default environment (None)"""
    with patch('app.core.env.os.getenv') as mock_getenv, \
         patch('app.core.env.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = None

        await load_env()

        # Verify correct .env files are loaded for default (dev behavior)
        assert mock_load_dotenv.call_count == 2
        mock_load_dotenv.assert_any_call(".env.local")
        mock_load_dotenv.assert_any_call(".env")


@pytest.mark.asyncio
async def test_load_env_unknown():
    """Test load_env function with unknown environment"""
    with patch('app.core.env.os.getenv') as mock_getenv, \
         patch('app.core.env.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = "unknown"

        await load_env()

        # Verify default .env file is loaded for unknown environment
        mock_load_dotenv.assert_called_once_with(".env")
