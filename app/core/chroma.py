from typing import Self, Optional

import chromadb
from parlant.adapters.vector_db.chroma import ChromaDatabase, ChromaCollection
from parlant.core.loggers import Logger
from parlant.core.nlp.embedding import EmbedderFactory, EmbeddingCacheProvider

from parlant.core.persistence.vector_database import (
    BaseDocument,
)

class ServerChromaDatabase(ChromaDatabase):
    def __init__(
        self,
        logger: Logger,
        host: str,
        port: int,
        embedder_factory: EmbedderFactory,
        embedding_cache_provider: EmbeddingCacheProvider,
    ) -> None:
        self.host = host
        self.port = port
        self._logger = logger
        self._embedder_factory = embedder_factory

        self.chroma_client: chromadb.api.ClientAPI
        self._collections: dict[str, ChromaCollection[BaseDocument]] = {}

        self._embedding_cache_provider = embedding_cache_provider

    async def __aenter__(self) -> Self:
        self.chroma_client = chromadb.HttpClient(host=self.host, port=self.port)
        return self

    async def __aexit__(
        self,
        exc_type: Optional[type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[object],
    ) -> None:
        pass

