from contextlib import AsyncExitStack
import os
from typing import TypeVar, Any, cast, Callable, Awaitable

import parlant.sdk as p
from lagom.environment import Env
from parlant.adapters.db.json_file import JSONFileDocumentDatabase
from parlant.core.agents import AgentStore, AgentDocumentStore
from parlant.core.canned_responses import CannedResponseStore, CannedResponseVectorStore
from parlant.core.capabilities import CapabilityStore, CapabilityVectorStore
from parlant.core.context_variables import ContextVariableStore, ContextVariableDocumentStore
from parlant.core.evaluations import EvaluationStore, EvaluationDocumentStore
from parlant.core.glossary import GlossaryStore, GlossaryVectorStore
from parlant.core.guideline_tool_associations import GuidelineToolAssociationStore, GuidelineToolAssociationDocumentStore
from parlant.core.guidelines import GuidelineStore, GuidelineDocumentStore
from parlant.core.journeys import JourneyStore, JourneyVectorStore
from parlant.core.loggers import  Logger
from parlant.core.nlp.embedding import EmbedderFactory, EmbeddingCache, Embedder
from parlant.core.nlp.service import NLPService
from parlant.core.persistence.document_database import DocumentDatabase
from parlant.core.persistence.vector_database import VectorDatabase
from parlant.core.relationships import RelationshipStore, RelationshipDocumentStore
from parlant.core.tags import TagStore, TagDocumentStore
from parlant.bin.server import PARLANT_HOME_DIR
from pymongo import AsyncMongoClient

from app.core.chroma import ServerChromaDatabase

T: TypeVar = TypeVar("T")

class ChromaEnv(Env):
    chroma_host: str
    chroma_port: int

async def setup_document_stores(container: p.Container) -> None:
    exit_stack = AsyncExitStack()
    mongo_client: object | None = None

    async def make_mongo_db(url: str, name: str) -> DocumentDatabase:
        nonlocal mongo_client

        from parlant.adapters.db.mongo_db import MongoDocumentDatabase

        if mongo_client is None:
            mongo_client = await exit_stack.enter_async_context(
                AsyncMongoClient[Any](url)
            )

        db = await exit_stack.enter_async_context(
            MongoDocumentDatabase(
                mongo_client=cast(AsyncMongoClient[Any], mongo_client),
                database_name=f"parlant_{name}",
                logger=container[p.Logger],
            )
        )

        return db

    async def make_persistable_store(t: type[T], spec: str, name: str, **kwargs: Any) -> T:
        store: T

        store = await exit_stack.enter_async_context(
            t(
                database=await make_mongo_db(spec, name),
                allow_migration=False,  # should come from the migrate arg of Server class
                **kwargs,
            )
        )

        return store

    extra_args = {"id_generator": container[p.IdGenerator]}
    func_args = [
        # buggy: (AgentStore, AgentDocumentStore, "agents", extra_args),
        (GuidelineStore, GuidelineDocumentStore, "guidelines", extra_args),
        (ContextVariableStore, ContextVariableDocumentStore, "context_variables", extra_args),
        (EvaluationStore, EvaluationDocumentStore, "evaluations", {}),
        (GuidelineToolAssociationStore, GuidelineToolAssociationDocumentStore, "guideline_tool_associations", extra_args),
        (RelationshipStore, RelationshipDocumentStore, "relationships", extra_args),
        (TagStore, TagDocumentStore, "tags", extra_args),
    ]
    for store_interface, store_implementation, name, kwargs in func_args:
        container[store_interface] = await make_persistable_store(
            store_implementation, os.getenv("DATABASE_URL"), name, **kwargs
        )

async def setup_vector_stores(container: p.Container) -> None:
    exit_stack = AsyncExitStack()
    shared_chroma_db: VectorDatabase | None = None
    embedder_factory = EmbedderFactory(container)

    async def get_shared_chroma_db() -> VectorDatabase:
        nonlocal shared_chroma_db
        if shared_chroma_db is None:
            shared_chroma_db = await exit_stack.enter_async_context(
                ServerChromaDatabase(
                    container[Logger],
                    container[ChromaEnv].chroma_host,
                    container[ChromaEnv].chroma_port,
                    embedder_factory,
                    lambda: container[EmbeddingCache],
                ),
            )
        return cast(VectorDatabase, shared_chroma_db)

    async def define_vector_store(
        store_interface: type,
        store_implementation: type,
        vector_db_factory: Callable[[], Awaitable[VectorDatabase]],
        document_db_filename: str,
        embedder_type_provider: Callable[[], Awaitable[type[Embedder]]],
        embedder_factory: EmbedderFactory,
    ) -> None:
        # override already defined transient stores
        vector_db = await vector_db_factory()
        document_db = await exit_stack.enter_async_context(
            JSONFileDocumentDatabase(
                container[Logger],
                PARLANT_HOME_DIR / document_db_filename,
                )
        )
        container[store_implementation] = await exit_stack.enter_async_context(
            store_implementation(
                id_generator=container[p.IdGenerator],
                vector_db=vector_db,
                document_db=document_db,
                embedder_type_provider=embedder_type_provider,
                embedder_factory=embedder_factory,
            )
        )
        container[store_interface] = lambda _c: container[store_implementation]

    async def get_embedder_type() -> type[Embedder]:
        return type(await container[NLPService].get_embedder())

    for store_interface, store_implementation, document_db_filename in [
        (GlossaryStore, GlossaryVectorStore, "glossary_tags.json"),
        (CannedResponseStore, CannedResponseVectorStore, "canned_responses.json"),
        (JourneyStore, JourneyVectorStore, "journey_associations.json"),
        (CapabilityStore, CapabilityVectorStore, "capabilities.json"),
    ]:
        container[Logger].debug(f"Defining {store_interface.__name__}")
        await define_vector_store(
            store_interface,
            store_implementation,
            lambda: get_shared_chroma_db(),
            document_db_filename,
            get_embedder_type,
            embedder_factory,
        )
