import asyncio
import os

import parlant.sdk as p
from parlant.core.agents import AgentStore, AgentUpdateParams
from parlant.core.canned_responses import CannedResponseStore
from parlant.core.loggers import LogLevel, Logger

from app.core.env import load_env
from app.core.persistance import setup_document_stores, setup_vector_stores

AGENT_ID = "VDIr5kNDWh"

async def configure_container(container: p.Container) -> p.Container:

    # Server has not been initialized completely, so we have to setup loglevel manually
    container[Logger].set_level(LogLevel[os.getenv("LOG_LEVEL", "INFO")])

    await setup_document_stores(container)
    await setup_vector_stores(container)

    container[Logger].debug("Container configured.")
    return container

async def initialize_container(container: p.Container) -> None:


    container[Logger].debug("Container initialized.")

async def main():
    print("Starting clippy...")
    await load_env()

    # We can't use GlobalEnv here because the container is not yet built
    async with p.Server(session_store=os.getenv("DATABASE_URL"),
                        customer_store=os.getenv("DATABASE_URL"),
                        port=int(os.getenv("APP_PORT")),
                        configure_container=configure_container,
                        initialize_container=initialize_container,
                        log_level=LogLevel[os.getenv("LOG_LEVEL", "INFO")]
                        ) as server:

        # FIXME: accessing protected attribute
        container = server._container

        # https://clippy.dev/agents
        agent = await server.find_agent(id=AGENT_ID)
        if agent is None:
            # Create an agent stub to get a predictable id
            agent = await server.create_agent(
                name="AGENT_1",
                description="",
            )
            assert agent.id == AGENT_ID
            container[Logger].debug("Created agent.")

            # Then mutate it with desired properties
            await container[AgentStore].update_agent(
                agent_id=agent.id,
                params= AgentUpdateParams(
                    name="Son-Vidéo IA",
                    description="Tu es un conseiller après-vente de Son-Vidéo, spécialisé dans la vente de produits audiovisuels.",
                    max_engine_iterations=3,
                    composition_mode=p.CompositionMode.FLUID
                )
            )

            agent = await server.get_agent(id=AGENT_ID)



        # exemples
        # create glossary
        '''await agent.create_term(
            name="PVAC",
            description="Paris Audio Video Show: l'évènement numéro 1 en europe sur l'audiovisuel.",
            synonyms=["Paris AudioVideo Show", "Paris Audio Video show"],
        )'''

        # create canned response
        '''await agent.create_canned_response(
            template="La sagesse est l'ennemi du désarroi.",
            tags=[p.Tag.preamble()],
            signals=["Bonjour."],
        )'''

        # create guideline
        '''guideline = await agent.create_guideline(
            condition="l'utilisateur demande un conseil",
            action="Refuse poliment en expliquant qu'il serait dangereux, illégal ou immoral de répondre. explique pourquoi."
        )'''

        # poor man's rehydrate vector db
        '''store = container[CannedResponseStore]
        for c in await store.list_canned_responses():
            await store.update_canned_response(c.id, {})'''

if __name__ == "__main__":
    asyncio.run(main())
